<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
    <script src="../../../js/JsBarcode.all.min.js" charset="UTF-8"></script>
</head>
<body>

<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">容器编码：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="eq_boxNo" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">容器类型：</label>
                    <div class="layui-input-inline">
                        <select type="text" name="eq_boxType" id="containerType" autocomplete="off" class="layui-input"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">料箱箱型：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="eq_boxContainerType" id="status" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit
                            lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset><i
                            class="layui-icon layui-icon-refresh-1"></i>重置
                    </button>
                </div>
            </div>
        </form>
    </div>

    <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
</div>

<script type="text/html" id="currentTableBar">
    <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除</a>
</script>

<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal" lay-event="add">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary" lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>
<!-- 操作列 -->
<script type="text/html" id="auth-state">
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><i class="layui-icon">&#xe642;</i>编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete"><i class="layui-icon">&#xe640;</i>删除</a>
</script>

<script>
    layui.use(['form', 'table'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;
        // $.operate.initDictDropDown("container_status", "status")
        $.operate.initDictDropDown("container_type","containerType","containerType")
        // $.operate.initDictDropDown("wms_container,container_no,container_type='pallet',status=0","status")获取可用容器号
        var tableObj = table.render({
            elem: '#currentTableId',
            url: ctx + 'container/list',
            method: 'post',
            contentType: 'application/json',
            toolbar: '#toolbarDemo',
            parseData: function (result) {
                return {
                    "code": (result.code == 200 ? '0' : result.code),
                    "data": result.data,
                    "count": result.total
                }
            },
            cols: [[
                {type: "checkbox", width: 50},
                {field: 'boxNo', width: 150, title: '容器编码', sort: true},
                {field: 'boxType', width: 150, title: '容器类型', sort: true,templet:function (d){
                        if(d.boxType == '2'){
                            return '料架';
                        }else if(d.boxType == '1'){
                            return '料箱';
                        }else{
                            return '';
                        }
                    }},
                {field: 'boxContainerType', width: 150, title: '箱型', sort: true},
                {field: 'boxEmptyStatus', width: 150, title: '使用状态', sort: true,},
                {field: 'createBy', width: 120, title: '创建人'},
                {field: 'createTime', width: 160, title: '创建时间'},
                {field: 'updateBy', width: 120, title: '更新人'},
                {field: 'updateTime', width: 160, title: '更新时间'},
                // {field: 'barcode', width: 100,title: '条码',templet:function (d) {
                //         return '<svg class='+d.containerNo+'></svg>'}},
                {templet: '#auth-state', width: 150, minWidth: 150, align: 'center', title: '操作'}
            ]],
            done:function(res, curr, count, origin){
                var options = {
                    format:"CODE128",
                    displayValue:true,
                    fontsize:18,
                    height:100
                };
                var data=res.data;
                for (let i = 0; i < data.length; i++) {
                    $("."+data[i].boxNo).JsBarcode(data[i].boxNo,options);
                }
            },
            limits: [10, 15, 20, 25, 50, 100,10000],
            limit: 10,
            page: true
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            switch (obj.event) {
                case 'add':
                    $.modal.openDrawer({
                        title: '新增容器',
                        url: "containerInfo.html",
                        layerCallBack: function (returnVal) {
                            table.reload('currentTableId');
                        }
                    });
                    break;
                case 'refresh':
                    table.reload('currentTableId');
                    break;
            }
            ;
        });

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                $.modal.openDrawer({
                    title: '修改容器信息',
                    url: "containerInfo.html?id=" + data.id,
                    layerCallBack: function (returnVal) {
                        table.reload('currentTableId');
                    }
                });
                return false;
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除行么', function (index) {
                    $.operate.postJson({
                        url: "container/delete",
                        data: data.id,
                        isShowMsg: true,
                        callback: function (result) {
                            if (result.code == 200) {
                                table.reload('currentTableId');
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });
    });
</script>
</body>
</html>