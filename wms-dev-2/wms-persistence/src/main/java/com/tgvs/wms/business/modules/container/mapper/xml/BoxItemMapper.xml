<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.container.mapper.BoxItemMapper">
<!--   根据机物料编码获取单个机物料库存信息-->
    <select id="selectMaterialCode" resultType="com.tgvs.wms.business.modules.container.entity.BoxItem">
        select *
        from wms_box_item
        <where>
            grid_status &lt;'3'
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
            <if test="boxType != null and boxType != ''">
                AND box_type = #{boxType}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectMaterialCodeStock" resultType="com.tgvs.wms.business.modules.container.entity.BoxItem">
        select *
        from wms_box_item
        <where>
            grid_status &lt;'3'
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
            <if test="boxType != null and boxType != ''">
                AND box_type = #{boxType}
            </if>
        </where>
        AND material_quantity>0
        order by create_time desc
    </select>

    <select id="selectMaterialCodeTray" resultType="com.tgvs.wms.business.modules.container.entity.BoxItem">
        select *
        from wms_box_item
        <where>
            grid_volume  &lt; '100'
            <if test="boxType != null and boxType != ''">
                AND box_type = #{boxType}
            </if>
        </where>
        order by grid_volume ASC
    </select>

    <select id="selectEmptyboxNo" resultType="com.tgvs.wms.business.modules.container.entity.BoxItem">
        select *
        from wms_box_item
        <where>
            <if test="boxNo != null and boxNo != ''">
                AND box_no = #{boxNo}
            </if>
        </where>
        AND delete_flag=0
        AND status=0
        order by grid_id ASC
    </select>

    <select id="selectTray" resultType="com.tgvs.wms.business.modules.container.entity.BoxItem">
        select *
        from wms_box_item
        <where>
            grid_volume BETWEEN 1 AND 99
            <if test="boxType != null and boxType != ''">
                AND box_type = #{boxType}
            </if>
        </where>
        order by grid_volume desc
    </select>

    <!--   获取单个容器指定机物料库存-->
    <select id="materialOutBound" resultType="com.tgvs.wms.business.modules.container.Dto.materialOutBoundDetailDTO">
        select * from
        (
        select box_no,material_code,sum(material_quantity) AS material_quantity from wms_box_item
        <where>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
        </where>
        GROUP BY box_no
        ) AS A
        where A.box_no NOT IN
        (
        SELECT box_no FROM wms_box_task_list
        WHERE task_status &lt;3
        AND delete_flag=0
        )
        <if test="quantity != null and quantity != ''">
            AND A.material_quantity >= #{quantity}
        </if>
        ORDER BY A.material_quantity LIMIT 1
    </select>

    <!--   获取指定机物料库存列表-->
    <select id="materialOutBoundList" resultType="com.tgvs.wms.business.modules.container.entity.BoxItem">

        select box_no,grid_id,material_code,sum(material_quantity) AS material_quantity from wms_box_item
        where 1=1
        <where>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
        </where>
        GROUP BY box_no,grid_id,material_code

    </select>

    <!--   根据机物料编码获取单个机物料库存信息-->
    <select id="checkInventory" resultType="com.tgvs.wms.business.modules.container.Dto.materialOutBoundDetailDTO">
        select material_code,sum(material_quantity) AS material_quantity from wms_box_item
        <where>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
        </where>
        GROUP BY material_code
    </select>

    <select id="materialBoxGridSum" resultType="com.tgvs.wms.business.modules.container.entity.BoxItem">
        select box_no,grid_id,material_quantity,sum(material_quantity) AS material_quantity
        from wms_box_item
        where 1=1
        <where>
            <if test="boxNo != null and boxNo != ''">
                AND box_no = #{boxNo}
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
        </where>
        GROUP BY box_no,grid_id,material_code
    </select>

    <!--   根据机物料编码获取料箱每一格的物料库存信息-->
    <select id="boxGridInventoryList" resultType="com.tgvs.wms.business.modules.container.Dto.materialOutBoundDetailDTO">
        select box_no,grid_id,sum(material_quantity) AS material_quantity
        from wms_box_item
        <where>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
        </where>
        GROUP BY box_no,grid_id
        ORDER BY material_quantity DESC
    </select>

</mapper>