<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryOutboundMapper">

    <!-- AuxiliaryOutListDto 的 ResultMap -->
    <resultMap id="AuxiliaryOutListDtoMap" type="com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryOutListDto">
        <!-- WmsAuxiliaryOutBound 父类字段 -->
        <result column="outbound_id" property="id"/>
        <result column="out_store_number" property="outStoreNumber"/>
        <result column="out_type" property="outType"/>
        <result column="status" property="status"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="remarks" property="remarks"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        
        <!-- AuxiliaryOutListDto 特有字段 -->
        <result column="out_store_number" property="refNumber"/>
        <result column="operation_type" property="operationType"/>
        <result column="contract_no" property="contractNo"/>
        <result column="item_no" property="itemNo"/>
        <result column="req_list_id" property="reqListId"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="material_color" property="materialColor"/>
        <result column="material_color_code" property="materialColorCode"/>
        <result column="material_model" property="materialModel"/>
        <result column="quantity" property="quantity"/>
        <result column="material_unit" property="materialUnit"/>
        <result column="priority" property="priority"/>
        <result column="material_type" property="materialType"/>
    </resultMap>

        <!-- 分页查询辅料出库信息（直接返回DTO） -->
    <select id="selectPage" resultMap="AuxiliaryOutListDtoMap">
        SELECT 
            a.id as outbound_id,
            a.out_store_number,
            a.out_type,
            a.status,
            a.delete_flag,
            a.remarks,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            d.id as detail_id,
            d.operation_type,
            d.contract_no,
            d.item_no,
            d.req_list_id,
            d.material_code,
            d.material_name,
            d.material_color,
            d.material_color_code,
            d.material_model,
            d.quantity,
            d.material_unit,
            d.priority,
            ai.material_type
        FROM wms_auxiliary_outbound a
        INNER JOIN wms_auxiliary_detail d ON a.out_store_number = d.ref_number AND d.operation_type = 1
        LEFT JOIN wms_auxiliary_info ai ON d.material_code = ai.material_code
        <where>
            a.delete_flag = 0 and d.delete_flag = 0 and d.prebox_status in(0,1)
            <if test="queryModel.searchParams.outStoreNumber != null and queryModel.searchParams.outStoreNumber != ''">
                AND a.out_store_number LIKE CONCAT('%', #{queryModel.searchParams.outStoreNumber}, '%')
            </if>
            <if test="queryModel.searchParams.outType != null">
                AND a.out_type = #{queryModel.searchParams.outType}
            </if>
            <if test="queryModel.searchParams.status != null">
                AND a.status = #{queryModel.searchParams.status}
            </if>
            <if test="queryModel.searchParams.contractNo != null and queryModel.searchParams.contractNo != ''">
                AND d.contract_no LIKE CONCAT('%', #{queryModel.searchParams.contractNo}, '%')
            </if>
            <if test="queryModel.searchParams.itemNo != null and queryModel.searchParams.itemNo != ''">
                AND d.item_no LIKE CONCAT('%', #{queryModel.searchParams.itemNo}, '%')
            </if>
            <if test="queryModel.searchParams.materialCode != null and queryModel.searchParams.materialCode != ''">
                AND d.material_code LIKE CONCAT('%', #{queryModel.searchParams.materialCode}, '%')
            </if>
            <if test="queryModel.searchParams.materialName != null and queryModel.searchParams.materialName != ''">
                AND d.material_name LIKE CONCAT('%', #{queryModel.searchParams.materialName}, '%')
            </if>
            <if test="queryModel.searchParams.materialModel != null and queryModel.searchParams.materialModel != ''">
                AND d.material_model LIKE CONCAT('%', #{queryModel.searchParams.materialModel}, '%')
            </if>
            <if test="queryModel.searchParams.materialColor != null and queryModel.searchParams.materialColor != ''">
                AND d.material_color LIKE CONCAT('%', #{queryModel.searchParams.materialColor}, '%')
            </if>
            <if test="queryModel.searchParams.materialType != null">
                AND ai.material_type = #{queryModel.searchParams.materialType}
            </if>
        </where>
        <if test="queryModel.sortList != null and queryModel.sortList.size() > 0">
            ORDER BY
            <foreach collection="queryModel.sortList" item="sort" separator=",">
                <choose>
                    <when test="sort.column == 'createTime'">a.create_time</when>
                    <when test="sort.column == 'updateTime'">a.update_time</when>
                    <when test="sort.column == 'outStoreNumber'">a.out_store_number</when>
                    <when test="sort.column == 'status'">a.status</when>
                    <when test="sort.column == 'materialCode'">d.material_code</when>
                    <when test="sort.column == 'materialName'">d.material_name</when>
                    <when test="sort.column == 'quantity'">d.quantity</when>
                    <otherwise>a.create_time</otherwise>
                </choose>
                <choose>
                    <when test="sort.sortType == 1">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </foreach>
        </if>
        <if test="queryModel.sortList == null or queryModel.sortList.size() == 0">
            ORDER BY a.create_time DESC
        </if>
    </select>

    <!-- 分页查询辅料出库信息（返回实体类） -->
    <select id="selectPageEntity" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutBound">
        SELECT 
            a.*,
            b.category,
            b.unit,
            b.capacity,
            b.priority_container_type,
            b.material_type,
            b.is_store
        FROM wms_auxiliary_outbound a
        INNER JOIN wms_auxiliary_detail d on a.out_store_number=d.ref_number
        LEFT JOIN wms_auxiliary_info b ON d.material_code = b.material_code
        <where>
            <if test="queryModel.searchParams.outStoreNumber != null and queryModel.searchParams.outStoreNumber != ''">
                AND a.out_store_number LIKE CONCAT('%', #{queryModel.searchParams.outStoreNumber}, '%')
            </if>
            <if test="queryModel.searchParams.contractNo != null and queryModel.searchParams.contractNo != ''">
                AND a.contract_no LIKE CONCAT('%', #{queryModel.searchParams.contractNo}, '%')
            </if>
            <if test="queryModel.searchParams.outType != null">
                AND a.out_type = #{queryModel.searchParams.outType}
            </if>
            <if test="queryModel.searchParams.itemNo != null and queryModel.searchParams.itemNo != ''">
                AND d.item_no LIKE CONCAT('%', #{queryModel.searchParams.itemNo}, '%')
            </if>
            <if test="queryModel.searchParams.materialName != null and queryModel.searchParams.materialName != ''">
                AND d.material_name LIKE CONCAT('%', #{queryModel.searchParams.materialName}, '%')
            </if>
            <if test="queryModel.searchParams.materialCode != null and queryModel.searchParams.materialCode != ''">
                AND d.material_code LIKE CONCAT('%', #{queryModel.searchParams.materialCode}, '%')
            </if>
            <if test="queryModel.searchParams.materialModel != null and queryModel.searchParams.materialModel != ''">
                AND d.material_model LIKE CONCAT('%', #{queryModel.searchParams.materialModel}, '%')
            </if>
            <if test="queryModel.searchParams.materialColor != null and queryModel.searchParams.materialColor != ''">
                AND d.material_color LIKE CONCAT('%', #{queryModel.searchParams.materialColor}, '%')
            </if>
            <if test="queryModel.searchParams.materialType != null">
                AND b.material_type = #{queryModel.searchParams.materialType}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper> 