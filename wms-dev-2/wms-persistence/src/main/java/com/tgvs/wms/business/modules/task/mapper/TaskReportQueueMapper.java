package com.tgvs.wms.business.modules.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.business.modules.task.entity.TaskReportQueue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * WMS任务上报ROS队列表 Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface TaskReportQueueMapper extends BaseMapper<TaskReportQueue> {

    /**
     * 分页查询待上报的任务
     * 
     * @param page 分页参数
     * @param reportType 上报类型：INBOUND/OUTBOUND
     * @param reportStatus 上报状态：0待上报，1已上报，2上报失败，3已取消
     * @param priority 优先级
     * @return 分页结果
     */
    IPage<TaskReportQueue> selectPendingReportTasks(
            Page<TaskReportQueue> page,
            @Param("reportType") String reportType,
            @Param("reportStatus") Integer reportStatus,
            @Param("priority") Integer priority
    );

    /**
     * 查询需要重试的任务列表
     * 
     * @param currentTime 当前时间
     * @param maxRetryCount 最大重试次数
     * @param limit 限制数量
     * @return 需要重试的任务列表
     */
    List<TaskReportQueue> selectRetryTasks(
            @Param("currentTime") Date currentTime,
            @Param("maxRetryCount") Integer maxRetryCount,
            @Param("limit") Integer limit
    );

    /**
     * 批量更新任务状态
     * 
     * @param taskIds 任务ID列表
     * @param reportStatus 新的上报状态
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @return 更新的记录数
     */
    int batchUpdateReportStatus(
            @Param("taskIds") List<Long> taskIds,
            @Param("reportStatus") Integer reportStatus,
            @Param("errorCode") String errorCode,
            @Param("errorMessage") String errorMessage
    );

    /**
     * 根据优先级和创建时间查询待上报任务
     * 
     * @param reportType 上报类型
     * @param reportStatus 上报状态
     * @param limit 限制数量
     * @return 任务列表
     */
    List<TaskReportQueue> selectTasksByPriorityAndTime(
            @Param("reportType") String reportType,
            @Param("reportStatus") Integer reportStatus,
            @Param("limit") Integer limit
    );

    /**
     * 统计上报任务数量
     * 
     * @param reportType 上报类型
     * @param reportStatus 上报状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数量
     */
    Long countReportTasks(
            @Param("reportType") String reportType,
            @Param("reportStatus") Integer reportStatus,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 查询上报成功率统计
     * 
     * @param reportType 上报类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率统计信息
     */
    List<Map<String, Object>> selectReportSuccessRate(
            @Param("reportType") String reportType,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 删除过期的已完成任务记录
     * 
     * @param expireTime 过期时间
     * @param reportStatus 上报状态（1-已上报）
     * @return 删除的记录数
     */
    int deleteExpiredCompletedTasks(
            @Param("expireTime") Date expireTime,
            @Param("reportStatus") Integer reportStatus
    );

    /**
     * 查询错误统计信息
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 错误统计列表
     */
    List<Map<String, Object>> selectErrorStatistics(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 重置失败任务的重试次数
     * 
     * @param taskIds 任务ID列表
     * @return 更新的记录数
     */
    int resetRetryCount(@Param("taskIds") List<Long> taskIds);

    /**
     * 查询长时间未上报的任务
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时任务列表
     */
    List<TaskReportQueue> selectTimeoutTasks(@Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 按任务类型统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务类型统计信息
     */
    List<Map<String, Object>> selectTaskTypeStatistics(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 按优先级统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 优先级统计信息
     */
    List<Map<String, Object>> selectPriorityStatistics(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 查询重试次数最多的任务
     * 
     * @param limit 限制数量
     * @return 重试次数最多的任务列表
     */
    List<TaskReportQueue> selectMostRetriedTasks(@Param("limit") Integer limit);

    /**
     * 根据任务单号查询物料信息用于ROS上报
     * 
     * @param taskOrder 任务单号
     * @return 物料信息列表，包含ROListID和Qty字段
     */
    List<Map<String, Object>> selectMatInfosByTaskOrder(@Param("taskOrder") String taskOrder);

    /**
     * 根据任务单号查询出库物料信息用于ROS上报
     * 
     * @param taskOrder 任务单号
     * @return 出库物料信息列表，包含ROListID和Qty字段
     */
    List<Map<String, Object>> selectOutboundMatInfosByTaskOrder(@Param("taskOrder") String taskOrder);
} 