package com.tgvs.wms.business.modules.machineAuxiliary.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInBound;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryInboundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 入库验证工具类
 * 用于处理入库单号重复等验证逻辑
 */
@Slf4j
@Component
public class InboundValidationUtil {

    /**
     * 验证入库单号是否可以使用
     * 
     * @param inStoreNumber 入库单号
     * @param inboundService 入库服务
     * @return 验证结果
     */
    public static ValidationResult validateInStoreNumber(String inStoreNumber, IAuxiliaryInboundService inboundService) {
        if (StringUtils.isEmpty(inStoreNumber)) {
            return ValidationResult.error("入库单号不能为空");
        }

        log.debug("验证入库单号: {}", inStoreNumber);
        
        // 查询现有记录
        WmsAuxiliaryInBound existingHeader = inboundService.getOne(new LambdaQueryWrapper<WmsAuxiliaryInBound>()
                .eq(WmsAuxiliaryInBound::getInStoreNumber, inStoreNumber)
                .eq(WmsAuxiliaryInBound::getDeleteFlag, 0) // 确保未删除
                .ne(WmsAuxiliaryInBound::getStatus, 3), false); // 状态不等于3表示已取消

        if (existingHeader == null) {
            log.debug("入库单号 {} 可以使用", inStoreNumber);
            return ValidationResult.success();
        }

        // 存在重复记录，根据状态返回不同的结果
        String statusDesc = getStatusDescription(existingHeader.getStatus());
        log.warn("发现重复入库单号: {}, 状态: {} ({})", inStoreNumber, existingHeader.getStatus(), statusDesc);

        switch (existingHeader.getStatus()) {
            case 0: // 待处理
                return ValidationResult.warning(
                    String.format("入库单号 %s 已存在且状态为待处理，如需重新提交请先取消原有入库单", inStoreNumber),
                    existingHeader
                );
            case 1: // 处理中
                return ValidationResult.error(
                    String.format("入库单号 %s 已存在且正在处理中，无法重复提交", inStoreNumber)
                );
            case 2: // 已完成
                return ValidationResult.error(
                    String.format("入库单号 %s 已存在且已完成，无法重复提交", inStoreNumber)
                );
            default:
                return ValidationResult.error(
                    String.format("入库单号 %s 已存在，当前状态: %s", inStoreNumber, statusDesc)
                );
        }
    }

    /**
     * 获取入库单状态描述
     */
    private static String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知状态";
        }
        switch (status) {
            case 0:
                return "待处理";
            case 1:
                return "处理中";
            case 2:
                return "已完成";
            case 3:
                return "已取消";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean success;
        private String message;
        private WmsAuxiliaryInBound existingRecord;
        private ValidationLevel level;

        public enum ValidationLevel {
            SUCCESS, WARNING, ERROR
        }

        private ValidationResult(boolean success, String message, ValidationLevel level, WmsAuxiliaryInBound existingRecord) {
            this.success = success;
            this.message = message;
            this.level = level;
            this.existingRecord = existingRecord;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, "验证通过", ValidationLevel.SUCCESS, null);
        }

        public static ValidationResult warning(String message, WmsAuxiliaryInBound existingRecord) {
            return new ValidationResult(false, message, ValidationLevel.WARNING, existingRecord);
        }

        public static ValidationResult error(String message) {
            return new ValidationResult(false, message, ValidationLevel.ERROR, null);
        }

        // Getters
        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public WmsAuxiliaryInBound getExistingRecord() {
            return existingRecord;
        }

        public ValidationLevel getLevel() {
            return level;
        }

        public boolean isWarning() {
            return level == ValidationLevel.WARNING;
        }

        public boolean isError() {
            return level == ValidationLevel.ERROR;
        }
    }
}
