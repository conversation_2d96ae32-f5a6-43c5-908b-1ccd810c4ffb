package com.tgvs.wms.business.modules.task.service.impl;

import com.tgvs.wms.business.modules.task.entity.TaskReportQueue;
import com.tgvs.wms.business.modules.task.mapper.TaskReportQueueMapper;
import com.tgvs.wms.business.modules.task.service.IRosDataIntegrationService;
import com.tgvs.wms.business.modules.task.service.IWmsBoxTaskListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ROS数据整合服务实现
 * 负责整合任务基础信息，构建完整的ROS接口入参
 */
@Slf4j
@Service
public class RosDataIntegrationService implements IRosDataIntegrationService {

    @Autowired
    private IWmsBoxTaskListService wmsBoxTaskListService;
    
    @Autowired
    private TaskReportQueueMapper taskReportQueueMapper;

    /**
     * 根据上报队列记录，整合完整的ROS接口入参
     */
    @Override
    public Map<String, Object> buildRosReportData(TaskReportQueue reportTask) {
        try {
            log.info("开始整合上报数据: taskOrder={}, reportType={}", 
                    reportTask.getTaskOrder(), reportTask.getReportType());

            if ("INBOUND".equals(reportTask.getReportType())) {
                return buildInboundData(reportTask);
            } else if ("OUTBOUND".equals(reportTask.getReportType())) {
                return buildOutboundData(reportTask);
            } else {
                throw new IllegalArgumentException("不支持的上报类型: " + reportTask.getReportType());
            }

        } catch (Exception e) {
            log.error("数据整合失败: taskOrder={}, error={}", 
                     reportTask.getTaskOrder(), e.getMessage(), e);
            throw new RuntimeException("数据整合失败: " + e.getMessage(), e);
        }
    }

    /**
     * 整合入库上报数据
     */
    private Map<String, Object> buildInboundData(TaskReportQueue reportTask) {
        log.debug("整合入库上报数据: taskOrder={}", reportTask.getTaskOrder());

        Map<String, Object> inboundData = new HashMap<>();
        
        // 固定库位ID
        inboundData.put("LocateID", "101");
        
        // 获取物料信息列表
        List<Map<String, Object>> matInfos = getMatInfosByTaskOrder(reportTask.getTaskOrder());
        inboundData.put("MatInfos", matInfos);
        
        log.info("入库数据整合完成: taskOrder={}, matInfosCount={}", 
                reportTask.getTaskOrder(), matInfos.size());
        
        return inboundData;
    }
    
    /**
     * 根据任务单号获取物料信息
     * @param taskOrder 任务单号
     * @return 物料信息列表
     */
    private List<Map<String, Object>> getMatInfosByTaskOrder(String taskOrder) {
        try {
            List<Map<String, Object>> rawResults = taskReportQueueMapper.selectMatInfosByTaskOrder(taskOrder);
            List<Map<String, Object>> matInfos = new ArrayList<>();
            
            for (Map<String, Object> row : rawResults) {
                Map<String, Object> matInfo = new HashMap<>();
                matInfo.put("ROListID", row.get("ROListID"));
                
                // 确保数量为数值类型
                Object qtyObj = row.get("Qty");
                BigDecimal qty = BigDecimal.ZERO;
                if (qtyObj instanceof BigDecimal) {
                    qty = (BigDecimal) qtyObj;
                } else if (qtyObj instanceof Number) {
                    qty = BigDecimal.valueOf(((Number) qtyObj).doubleValue());
                } else if (qtyObj != null) {
                    try {
                        qty = new BigDecimal(qtyObj.toString());
                    } catch (NumberFormatException e) {
                        log.warn("数量格式转换失败: {}, 使用默认值0", qtyObj);
                    }
                }
                
                matInfo.put("Qty", qty.doubleValue());
                matInfos.add(matInfo);
            }
            
            log.debug("获取到物料信息: taskOrder={}, count={}", taskOrder, matInfos.size());
            return matInfos;
            
        } catch (Exception e) {
            log.error("获取物料信息失败: taskOrder={}, error={}", taskOrder, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 整合出库上报数据
     */
    private Map<String, Object> buildOutboundData(TaskReportQueue reportTask) {
        log.debug("整合出库上报数据: taskOrder={}", reportTask.getTaskOrder());

        Map<String, Object> outboundData = new HashMap<>();
        
        // 固定库位ID
        outboundData.put("LocateID", "101");
        
        // 获取出库物料信息列表（使用不同的查询逻辑）
        List<Map<String, Object>> matInfos = getOutboundMatInfosByTaskOrder(reportTask.getTaskOrder());
        outboundData.put("MatInfos", matInfos);
        
        log.info("出库数据整合完成: taskOrder={}, matInfosCount={}", 
                reportTask.getTaskOrder(), matInfos.size());
        
        return outboundData;
    }

    /**
     * 根据任务单号获取出库物料信息
     * @param taskOrder 任务单号
     * @return 出库物料信息列表
     */
    private List<Map<String, Object>> getOutboundMatInfosByTaskOrder(String taskOrder) {
        try {
            List<Map<String, Object>> rawResults = taskReportQueueMapper.selectOutboundMatInfosByTaskOrder(taskOrder);
            List<Map<String, Object>> matInfos = new ArrayList<>();
            
            for (Map<String, Object> row : rawResults) {
                Map<String, Object> matInfo = new HashMap<>();
                matInfo.put("ReqListID", row.get("ReqListID"));
                
                // 确保数量为数值类型
                Object qtyObj = row.get("Qty");
                BigDecimal qty = BigDecimal.ZERO;
                if (qtyObj instanceof BigDecimal) {
                    qty = (BigDecimal) qtyObj;
                } else if (qtyObj instanceof Number) {
                    qty = BigDecimal.valueOf(((Number) qtyObj).doubleValue());
                } else if (qtyObj != null) {
                    try {
                        qty = new BigDecimal(qtyObj.toString());
                    } catch (NumberFormatException e) {
                        log.warn("出库数量格式转换失败: {}, 使用默认值0", qtyObj);
                    }
                }
                
                matInfo.put("Qty", qty.doubleValue());
                matInfos.add(matInfo);
            }
            
            log.debug("获取到出库物料信息: taskOrder={}, count={}", taskOrder, matInfos.size());
            return matInfos;
            
        } catch (Exception e) {
            log.error("获取出库物料信息失败: taskOrder={}, error={}", taskOrder, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取任务类型描述
     */
    private String getTaskTypeDesc(Integer taskType) {
        if (taskType == null) {
            return "未知";
        }
        
        switch (taskType) {
            case 0: return "采购入库";
            case 1: return "调拨入库";
            case 2: return "生产退料回库";
            case 3: return "领料出库";
            case 4: return "调拨出库";
            case 5: return "采购退货出库";
            case 6: return "指定出库";
            case 7: return "指定入库";
            case 8: return "紧急出库";
            case 10: return "盘点出库";
            default: return "未知类型(" + taskType + ")";
        }
    }

    /**
     * 获取箱型描述
     */
    private String getBoxTypeDesc(Integer boxType) {
        if (boxType == null) {
            return "未知";
        }
        
        switch (boxType) {
            case 1: return "料箱";
            case 2: return "托盘";
            default: return "未知箱型(" + boxType + ")";
        }
    }

    /**
     * 获取物料类型描述
     */
    private String getMaterialTypeDesc(Integer materialType) {
        if (materialType == null) {
            return "未知";
        }
        
        switch (materialType) {
            case 1: return "生产物料";
            case 2: return "辅料";
            case 3: return "其他";
            default: return "未知物料(" + materialType + ")";
        }
    }

    /**
     * 验证数据完整性
     */
    @Override
    public boolean validateReportData(Map<String, Object> reportData) {
        try {
            // 验证必要字段
            String[] requiredFields = {"taskOrder", "taskType", "boxNo", "reportType", "timestamp"};
            
            for (String field : requiredFields) {
                if (!reportData.containsKey(field) || reportData.get(field) == null) {
                    log.warn("数据验证失败，缺少必要字段: {}", field);
                    return false;
                }
            }
            
            // 验证业务逻辑
            String reportType = (String) reportData.get("reportType");
            if (!"INBOUND".equals(reportType) && !"OUTBOUND".equals(reportType)) {
                log.warn("数据验证失败，无效的上报类型: {}", reportType);
                return false;
            }
            
            log.debug("数据验证通过: taskOrder={}", reportData.get("taskOrder"));
            return true;
            
        } catch (Exception e) {
            log.error("数据验证异常: {}", e.getMessage(), e);
            return false;
        }
    }
} 